/**
 * API配置文件
 * 管理所有API相关的配置和请求方法
 */

const apiConfig = {
  // ==================== 基础配置 ====================
  
  // API基础URL
  baseUrl: 'https://adminfrontend.welshine.com/wxapp/api',
  
  // 默认请求头
  defaultHeaders: {
    'content-type': 'application/json'
  },
  
  // 默认超时时间（毫秒）
  timeout: 10000,
  
  // ==================== 接口地址 ====================
  
  endpoints: {
    // 检查打印机设备
    checkPrinterDevice: '/PrinterManager/CheckPrinterDevice'
  },
  
  // ==================== 默认参数 ====================
  
  // 默认应用ID
  defaultAppId: 1,
  
  // 默认设备型号
  defaultModel: 'T50PRO'
}

/**
 * 生成请求ID
 */
function generateRequestId() {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

/**
 * 获取用户ID
 */
function getUserId() {
  return wx.getStorageSync('uid') || 'anonymous_user'
}

/**
 * 构造标准请求头
 */
function buildRequestHead(customParams = {}) {
  return {
    requestId: generateRequestId(),
    uid: getUserId(),
    appId: apiConfig.defaultAppId,
    ...customParams
  }
}

/**
 * 检查打印机设备API
 * @param {Object} device - 设备信息
 * @param {string} device.name - 设备名称/序列号
 * @param {string} device.deviceId - 设备ID
 * @param {string} model - 设备型号，默认为T50PRO
 * @returns {Promise} 返回检查结果
 */
function checkPrinterDevice(device, model = apiConfig.defaultModel) {
  return new Promise((resolve, reject) => {
    const requestData = {
      head: buildRequestHead(),
      body: {
        model: model,
        sn: device.name || device.deviceId
      }
    }

    console.log('检查打印机设备请求:', requestData)

    wx.request({
      url: apiConfig.baseUrl + apiConfig.endpoints.checkPrinterDevice,
      method: 'POST',
      header: apiConfig.defaultHeaders,
      data: requestData,
      timeout: apiConfig.timeout,
      success: (res) => {
        console.log('设备检查响应:', res)
        
        if (res.statusCode === 200 && res.data) {
          if (res.data.head && res.data.head.code === '200') {
            // 设备在库，检查成功
            resolve({
              success: true,
              isInStock: res.data.result?.isInStock || false,
              deviceInfo: res.data.result
            })
          } else {
            // 设备不在库或其他错误
            resolve({
              success: false,
              message: res.data.head?.message || '设备检查失败',
              code: res.data.head?.code
            })
          }
        } else {
          reject(new Error('网络请求失败'))
        }
      },
      fail: (error) => {
        console.error('设备检查请求失败:', error)
        reject(error)
      }
    })
  })
}

module.exports = {
  apiConfig,
  generateRequestId,
  getUserId,
  buildRequestHead,
  checkPrinterDevice
}
